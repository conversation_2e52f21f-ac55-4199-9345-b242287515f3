# 🎯 **Live Room Ex Diamond (Sàn kc) - Tích hợp với getData API**

## 📋 **Tổng quan**

<PERSON>ệ thống "Sàn kc" đã được **tích hợp vào API `getData`** để tự động phân phối room khi "Out of data".

### 🔄 **Luồng hoạt động mới:**

1. **API `getData` được gọi** với `token` và `selfid`
2. **Nếu có data** → Trả về data bình thường
3. **Nếu "Out of data"** → Hệ thống tự động:
   - Kiểm tra user có room nào sẵn sàng không
   - Kiểm tra điều kiện diamond balance > 50
   - Kiểm tra có phải diamond cao nhất trong 3 phút không
   - **Nếu đủ điều kiện** → Phân phối room từ Sàn kc
   - **Nếu không đủ điều kiện** → Trả về room mặc định

## 🎯 **Ưu điểm:**

- **Tự động hóa**: Không cần gọi API riêng cho room
- **Thông minh**: Chỉ phân phối khi đủ điều kiện
- **Bảo mật**: Chỉ phân phối room của chính user đó
- **Linh hoạt**: Fallback về room mặc định nếu không có room

## 🔧 **API Response mới:**

### Khi "Out of data" và có room phân phối:
```json
{
    "status": "error",
    "message": "Out of data", 
    "roomLiveLink": "https://slink.bigovideo.tv/ABC123",  // Room từ Sàn kc
    "device_name": "iPhone 12"
}
```

### Khi "Out of data" nhưng không đủ điều kiện:
```json
{
    "status": "error",
    "message": "Out of data",
    "roomLiveLink": "https://slink.bigovideo.tv/NGAd7w",  // Room mặc định
    "device_name": "iPhone 12"
}
```

## 📊 **Điều kiện phân phối:**

1. **User có room sẵn sàng** (`is_active = true`, `is_used = false`)
2. **Diamond balance > 50**
3. **Diamond balance cao nhất trong 3 phút gần nhất**
4. **Room thuộc về user đó** (user_token khớp)

## 🎉 **Kết quả:**

- **Tự động tạo lịch sử phân phối** khi room được phân phối
- **Đánh dấu room đã sử dụng** để tránh phân phối lại
- **Theo dõi được trong bảng lịch sử** của trang Sàn kc
