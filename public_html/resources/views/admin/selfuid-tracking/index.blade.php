@extends('layouts.master')

@section('title')
    <title>SelfUID Tracking - Admin Panel</title>
@endsection

@section('content')

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: flex;">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <div class="loading-text mt-3">
                <h5>Đang tải dữ liệu...</h5>
                <p class="text-muted"><PERSON>ui lòng chờ trong giây lát</p>
            </div>
        </div>
    </div>

    <script>
        // Hiển thị loading ngay lập tức khi trang bắt đầu load
        document.addEventListener('DOMContentLoaded', function() {
            var loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }
        });

        // Xử lý khi người dùng bấm back/forward (pageshow event)
        window.addEventListener('pageshow', function(event) {
            // Nếu trang được load từ cache (bfcache)
            if (event.persisted) {
                var loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            }
        });
    </script>

    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">SelfUID Tracking</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">SelfUID Tracking</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Optimization Info -->

            <!-- Filter Form -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Bộ lọc</h3>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('selfuid-tracking.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>User Token</label>
                                    <select name="user_token" class="form-control select2">
                                        <option value="">-- Tất cả --</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->user_token }}"
                                                {{ request('user_token') == $user->user_token ? 'selected' : '' }}>
                                                {{ $user->name }} ({{ $user->user_token }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Self UID</label>
                                    <input type="text" name="self_uid" class="form-control"
                                           value="{{ request('self_uid') }}" placeholder="Tìm theo Self UID">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>IP Address</label>
                                    <input type="text" name="ip_address" class="form-control"
                                           value="{{ request('ip_address') }}" placeholder="Tìm theo IP">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Từ ngày</label>
                                    <input type="date" name="date_from" class="form-control"
                                           value="{{ request('date_from', \Carbon\Carbon::now()->subDays(6)->format('Y-m-d')) }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Đến ngày</label>
                                    <input type="date" name="date_to" class="form-control"
                                           value="{{ request('date_to', \Carbon\Carbon::now()->format('Y-m-d')) }}">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Sắp xếp theo</label>
                                    <select name="sort_by" class="form-control">
                                        <option value="updated_at" {{ request('sort_by') == 'updated_at' ? 'selected' : '' }}>Thời gian</option>
                                        <option value="diamond_balance" {{ request('sort_by') == 'diamond_balance' ? 'selected' : '' }}>Diamond</option>
                                        <option value="bean_balance" {{ request('sort_by') == 'bean_balance' ? 'selected' : '' }}>Bean</option>
                                        <option value="ip_address" {{ request('sort_by') == 'ip_address' ? 'selected' : '' }}>IP Address</option>
                                        <option value="self_uid" {{ request('sort_by') == 'self_uid' ? 'selected' : '' }}>Self UID</option>
                                        <option value="device_name" {{ request('sort_by') == 'device_name' ? 'selected' : '' }}>Device Name</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Thứ tự</label>
                                    <select name="sort_direction" class="form-control">
                                        <option value="desc" {{ request('sort_direction') == 'desc' ? 'selected' : '' }}>Giảm dần</option>
                                        <option value="asc" {{ request('sort_direction') == 'asc' ? 'selected' : '' }}>Tăng dần</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary" id="search-btn">
                                        <i class="fas fa-search"></i> Tìm kiếm
                                    </button>
                                    <a href="{{ route('selfuid-tracking.index') }}" class="btn btn-secondary" id="reset-btn">
                                        <i class="fas fa-undo"></i> Reset
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="row mb-4">
                {{-- Self UID --}}
                <div class="col-md-3">
                    <div class="info-box bg-success">
                        <span class="info-box-icon"><i class="fas fa-users"></i></span> {{-- Icon cho Self UID --}}
                        <div class="info-box-content">
                            <span class="info-box-text">Tổng Self UID</span>
                            <span class="info-box-number">{{ number_format($overallStats['uniqueSelfUidsCount']) }}</span>
                        </div>
                    </div>
                </div>
                {{-- Running (Self UID trong 30 phút gần đây) --}}
                <div class="col-md-3">
                    <div class="info-box bg-primary">
                        <span class="info-box-icon"><i class="fas fa-running"></i></span> {{-- Icon cho Running --}}
                        <div class="info-box-content">
                            <span class="info-box-text">Đang hoạt động (30 phút)</span>
                            <span class="info-box-number">{{ number_format($overallStats['recentRequestedSelfUidsCount']) }}</span>
                        </div>
                    </div>
                </div>
                {{-- Total Diamond --}}
                <div class="col-md-3">
                    <div class="info-box bg-warning">
                        <span class="info-box-icon"><i class="fas fa-gem"></i></span> {{-- Icon cho Diamond --}}
                        <div class="info-box-content">
                            <span class="info-box-text">Tổng Diamond</span>
                            <span class="info-box-number">{{ number_format($overallStats['totalDiamond']) }}</span>
                        </div>
                    </div>
                </div>
                {{-- Total Bean --}}
                <div class="col-md-3">
                    <div class="info-box bg-info">
                        <span class="info-box-icon"><i class="fas fa-coins"></i></span> {{-- Icon cho Bean --}}
                        <div class="info-box-content">
                            <span class="info-box-text">Tổng Bean</span>
                            <span class="info-box-number">{{ number_format($overallStats['totalBean']) }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Daily Statistics -->
            @if($dailyStats->count() > 0)
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Thống kê theo ngày</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>Ngày</th>
                                    <th>Số UID thay đổi</th>
                                    <th>Số lần tăng/giảm</th>
                                    <th>Tổng Diamond dao động</th>
                                    <th>Tổng Bean dao động</th>
                                    <th>Tổng Diamond</th>
                                    <th>Tổng Bean</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($dailyStats as $stat)
                                    <tr>
                                        <!-- ngày -->
                                        <td>
                                            <strong>{{ \Carbon\Carbon::parse($stat->date)->format('d/m/Y') }}</strong>
                                            <br><small class="text-muted">{{ \Carbon\Carbon::parse($stat->date)->format('l') }}</small>
                                        </td>
                                        <!-- Số UID thay đổi -->
                                        <td>
                                            <span class="badge badge-primary">{{ $stat->unique_self_uids }}</span>
                                            @if(isset($stat->unique_users_count) && $stat->unique_users_count > 0)
                                                <br><small class="text-muted">{{ $stat->unique_users_count }} users</small>
                                            @endif
                                        </td>
                                        <!-- Số lần tăng/giảm -->
                                        <td>
                                            @if(isset($stat->increase_count) && isset($stat->decrease_count))
                                                <span class="badge badge-success">↑{{ $stat->increase_count }}</span>
                                                <span class="badge badge-danger">↓{{ $stat->decrease_count }}</span>
                                            @else
                                                <span class="badge badge-secondary">N/A</span>
                                            @endif
                                        </td>
                                        <!-- Tổng Diamond dao động -->
                                        <td>
                                            @if(isset($stat->total_diamond_increase) && isset($stat->total_diamond_decrease))
                                                <div class="text-center">
                                                    <a href="{{ route('selfuid-tracking.daily-stats', array_merge(['date' => $stat->date], request()->only(['user_token', 'self_uid']))) }}"
                                                       class="text-decoration-none">
                                                        <span class="badge badge-success badge-sm">
                                                            <i class="fas fa-gem"></i> +{{ number_format($stat->total_diamond_increase) }}
                                                        </span>
                                                        <br>
                                                        <span class="badge badge-danger badge-sm">
                                                            <i class="fas fa-gem"></i> -{{ number_format($stat->total_diamond_decrease) }}
                                                        </span>
                                                    </a>
                                                    <br>
                                                    <small class="text-muted">Click để xem chi tiết</small>
                                                </div>
                                            @else
                                                <div class="text-center">
                                                    <a href="{{ route('selfuid-tracking.daily-stats', array_merge(['date' => $stat->date], request()->only(['user_token', 'self_uid']))) }}"
                                                       class="text-decoration-none">
                                                        <span class="badge badge-light">{{ number_format($stat->diamond_change ?? 0) }}</span>
                                                    </a>
                                                    <br>
                                                    <small class="text-muted">Click để xem chi tiết {{ $stat->date }}</small>
                                                </div>
                                            @endif
                                        </td>
                                        <!-- Tổng Bean dao động -->
                                        <td>
                                            @if(isset($stat->total_bean_increase) && isset($stat->total_bean_decrease))
                                                <span class="badge badge-success badge-sm">+{{ number_format($stat->total_bean_increase) }}</span>
                                                <br>
                                                <span class="badge badge-danger badge-sm">-{{ number_format($stat->total_bean_decrease) }}</span>
                                            @else
                                                <span class="badge badge-secondary">{{ number_format($stat->bean_change ?? 0) }}</span>
                                            @endif
                                        </td>
                                        <!-- Tổng Diamond -->
                                        <td>
                                            <span class="badge badge-warning">
                                                <i class="fas fa-gem"></i> {{ number_format($stat->total_diamond) }}
                                            </span>
                                        </td>
                                        <!-- Tổng Bean -->
                                        <td>
                                            <span class="badge badge-info">{{ number_format($stat->total_bean) }}</span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif



            <!-- Data Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Danh sách Self UID</h3>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-left mt-3">
                        {{ $selfUids->appends(request()->query())->links('pagination::bootstrap-4') }}
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>User Token</th>
                                    <th>
                                        Self UID
                                        @if(request('sort_by') == 'self_uid')
                                            @if(request('sort_direction') == 'asc')
                                                <i class="fas fa-sort-up text-primary"></i>
                                            @else
                                                <i class="fas fa-sort-down text-primary"></i>
                                            @endif
                                        @endif
                                    </th>
                                    <th>
                                        Diamond
                                        @if(request('sort_by') == 'diamond_balance')
                                            @if(request('sort_direction') == 'asc')
                                                <i class="fas fa-sort-up text-primary"></i>
                                            @else
                                                <i class="fas fa-sort-down text-primary"></i>
                                            @endif
                                        @endif
                                    </th>
                                    <th>
                                        Bean
                                        @if(request('sort_by') == 'bean_balance')
                                            @if(request('sort_direction') == 'asc')
                                                <i class="fas fa-sort-up text-primary"></i>
                                            @else
                                                <i class="fas fa-sort-down text-primary"></i>
                                            @endif
                                        @endif
                                    </th>
                                    <th>
                                        IP Address
                                        @if(request('sort_by') == 'ip_address')
                                            @if(request('sort_direction') == 'asc')
                                                <i class="fas fa-sort-up text-primary"></i>
                                            @else
                                                <i class="fas fa-sort-down text-primary"></i>
                                            @endif
                                        @endif
                                    </th>
                                    <th>
                                        Device Name
                                        @if(request('sort_by') == 'device_name')
                                            @if(request('sort_direction') == 'asc')
                                                <i class="fas fa-sort-up text-primary"></i>
                                            @else
                                                <i class="fas fa-sort-down text-primary"></i>
                                            @endif
                                        @endif
                                    </th>
                                    <th class="sortable" data-sort="last_diamond_change" style="cursor: pointer;">
                                        Ghi nhận điểm gần nhất
                                        @if(request('sort_by') == 'last_diamond_change' || !request('sort_by'))
                                            @if(request('sort_direction') == 'asc')
                                                <i class="fas fa-sort-up text-primary"></i>
                                            @else
                                                <i class="fas fa-sort-down text-primary"></i>
                                            @endif
                                        @else
                                            <i class="fas fa-sort text-muted"></i>
                                        @endif
                                    </th>
                                    <th>Ngày giờ request</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($selfUids as $selfUid)
                                    <tr>
                                        <td>{{ $selfUid->id }}</td>
                                        <td>
                                            <span class="badge badge-primary">{{ $selfUid->user_token }}</span>
                                            @if($selfUid->user)
                                                <br><small>{{ $selfUid->user->name }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $selfUid->self_uid) }}"
                                               class="text-primary font-weight-bold">
                                                {{ $selfUid->self_uid }}
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning">
                                                {{ number_format($selfUid->diamond_balance) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-success">
                                                {{ number_format($selfUid->bean_balance) }}
                                            </span>
                                        </td>
                                        <td>{{ $selfUid->ip_address ?: 'N/A' }}</td>
                                        <td>{{ $selfUid->device_name ?: 'N/A' }}</td>
                                        <td>
                                            @if($selfUid->last_diamond_change)
                                                @php
                                                    $changeTime = \Carbon\Carbon::parse($selfUid->last_diamond_change);
                                                    $diffInMinutes = $changeTime->diffInMinutes(now());
                                                @endphp
                                                <span class="text-primary">
                                                    ({{ $diffInMinutes }} phút trước)
                                                </span>
                                                <br>
                                                <small>{{ $changeTime->format('d/m/Y H:i:s') }}</small>
                                            @else
                                                <span class="text-muted">Chưa có thay đổi</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $selfUid->requested_at }}</small>
                                            <!-- <a href="{{ route('selfuid-tracking.show', $selfUid->self_uid) }}"
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> Chi tiết
                                            </a> -->
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center">Không có dữ liệu</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-left mt-3">
                        {{ $selfUids->appends(request()->query())->links('pagination::bootstrap-4') }}
                    </div>
                </div>
            </div>
        </div>
    </section>

@endsection

@section('script')
<script>
$(document).ready(function() {
    $('.select2').select2({
        theme: 'bootstrap4'
    });

    // Ẩn loading sau khi DOM và tất cả nội dung đã load xong
    $(window).on('load', function() {
        hideLoading();
    });

    // Xử lý khi trang được hiển thị (bao gồm cả khi back/forward)
    $(window).on('pageshow', function(event) {
        if (event.originalEvent.persisted) {
            hideLoading();
        }
    });

    // Hiển thị loading khi submit form tìm kiếm
    $('#search-btn').on('click', function(e) {
        showLoading();
    });

    // Hiển thị loading khi click reset
    $('#reset-btn').on('click', function(e) {
        showLoading();
    });

    // Hiển thị loading khi click pagination links
    $(document).on('click', '.pagination a', function(e) {
        showLoading();
    });

    // Hiển thị loading khi click vào các link chi tiết
    $(document).on('click', 'a[href*="selfuid-tracking"]', function(e) {
        // Chỉ hiển thị loading cho các link nội bộ, không phải link external
        if (this.hostname === window.location.hostname) {
            showLoading();
        }
    });

    function showLoading() {
        $('#loading-overlay').fadeIn(200);
    }

    function hideLoading() {
        $('#loading-overlay').fadeOut(300);
    }

    // Backup: Ẩn loading sau 1 giây nếu window.load không trigger
    setTimeout(function() {
        if ($('#loading-overlay').is(':visible')) {
            hideLoading();
        }
    }, 1000);
});
</script>
<style>
/* Loading Overlay Styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 9999;
    display: none; /* Ẩn ban đầu */
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(2px);
}

.loading-content {
    text-align: center;
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e3e6f0;
}

.loading-content .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
}

.loading-text h5 {
    color: #5a5c69;
    margin-bottom: 10px;
    font-weight: 600;
}

.loading-text p {
    color: #858796;
    margin-bottom: 0;
    font-size: 14px;
}

/* .pagination {
    margin: 0;
    justify-content: center;
}
.pagination .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border: 1px solid #dee2e6;
    color: #6c757d;
    min-width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.pagination .page-item {
    margin: 0 1px;
}
.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    font-weight: 500;
}
.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
} */
/* Fix SVG icon size với selector mạnh hơn */
/* .card-footer .pagination .page-link svg,
.pagination .page-link svg.w-5,
.pagination .page-link svg.h-5 {
    width: 12px !important;
    height: 12px !important;
    max-width: 12px !important;
    max-height: 12px !important;
} */
/* Override Tailwind classes */
.w-5, .h-5 {
    width: 12px !important;
    height: 12px !important;
}
/* Select2 styling */
.select2-container--bootstrap4 .select2-selection--single {
    border: 2px solid #ced4da !important;
    border-radius: 0.375rem !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.select2-container--bootstrap4 .select2-selection--single:hover {
    border-color: #007bff !important;
    cursor: pointer;
}
.select2-container--bootstrap4.select2-container--focus .select2-selection--single {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* Sortable table headers */
.sortable {
    user-select: none;
    transition: background-color 0.2s ease;
}

.sortable:hover {
    background-color: #f8f9fa !important;
}

.sortable i {
    margin-left: 5px;
    font-size: 0.8em;
}

.sortable .fa-sort {
    opacity: 0.5;
}

.sortable:hover .fa-sort {
    opacity: 0.8;
}
</style>

<script>
$(document).ready(function() {
    // Xử lý click vào header để sắp xếp
    $('.sortable').on('click', function(e) {
        e.preventDefault();

        var sortField = $(this).data('sort');
        var currentSort = '{{ request("sort_by") }}';
        var currentDirection = '{{ request("sort_direction") }}';
        var newDirection = 'desc';

        // Nếu đang sort theo cột này, thì đổi direction
        if (currentSort === sortField) {
            newDirection = currentDirection === 'desc' ? 'asc' : 'desc';
        }

        // Tạo URL mới với parameters sort
        var url = new URL(window.location.href);
        url.searchParams.set('sort_by', sortField);
        url.searchParams.set('sort_direction', newDirection);

        // Chuyển trang
        window.location.href = url.toString();
    });
});
</script>
@endsection
