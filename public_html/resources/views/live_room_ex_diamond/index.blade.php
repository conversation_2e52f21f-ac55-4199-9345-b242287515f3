@extends('layouts.master')
@section('title')
    <title>Sàn kc</title>
@endsection
@section('style')
<style>
.select2-container .select2-selection--single{
    height: 38px;
}
.select2-container--default .select2-results__option[aria-disabled=true] {
    color: #000;
}
</style>
@endsection
@section('content')
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
      <div class="row mb-2">
        <div class="col-sm-8">
          <h1 class="m-0">Sàn kc @if(Auth::user()->role == 1 && $user->role != 1)- Thành viên: {{ $user->name }} - {{ $user->email }} @endif</h1>
        </div><!-- /.col -->
        <div class="col-sm-4">
          <ol class="breadcrumb float-sm-right">
            <li class="breadcrumb-item"><a href="#">Home</a></li>
            <li class="breadcrumb-item active">Sàn kc</li>
          </ol>
        </div><!-- /.col -->
      </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
  <!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-lg-4">
                            <div class="form-inline">
                                <div class="input-group" data-widget="sidebar-search">
                                <input class="form-control" type="text" placeholder="Tìm kiếm..." id="search-btn" class="aria-label">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-8 group-btn text-right">
                            <button type="button" class="btn btn-primary add_room" data-container=".room_modal"
                            data-href="{{ route('live-room-ex-diamond.create', $token) }}"><i class="fa fa-plus"></i> Thêm phòng</button>
                        </div>
                    </div>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                    <table id="room_table" class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th>Tên phòng</th>
                            <th>URL Phòng</th>
                            <th>External Link</th>
                            <th>Trạng thái</th>
                            <th>Tình trạng</th>
                            <th>Số lần dùng</th>
                            <th>Thời gian tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    </table>
                </div>
                <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>

        <!-- History Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history mr-2"></i>
                            Lịch sử phân phối
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="history_table" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Thông tin phòng</th>
                                        <th>Self UID</th>
                                        <th>Diamond Balance</th>
                                        <th>Trạng thái</th>
                                        <th>KC đã dùng</th>
                                        <th>Thời gian phân phối</th>
                                        <th>Thời gian thành công</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade room_modal" id="room_modal" tabindex="-1" role="dialog">
        </div>
    </div><!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection
@section('script')
<script>
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    var debounceTripDetail = null;
    $('#search-btn').on('input', function(){
        clearTimeout(debounceTripDetail);
        debounceTripDetail = setTimeout(() => {
            room_table.search($(this).val()).draw();
        }, 500);
    });

    var room_table = $('#room_table').DataTable({
        "destroy": true,
        "lengthChange": false,
        "searching": true,
        "ordering": false,
        "info": true,
        "autoWidth": false,
        "responsive": true,
        "pageLength": 15,
        "pagingType": "full_numbers",
        "language": {
            "info": 'Hiển thị _START_ đến _END_ của _TOTAL_ mục',
            "infoEmpty": 'Hiển thị 0 đến 0 của 0 mục',
            "infoFiltered": '',
            "infoPostFix": "",
            "thousands": ",",
            "lengthMenu": 'Hiển thị _MENU_ mục',
            "loadingRecords": 'Đang tải...',
            "processing": 'Đang xử lý...',
            "emptyTable": 'Không có dữ liệu',
            "zeroRecords": 'Không tìm thấy kết quả',
            "search": 'Tìm kiếm',
            "paginate": {
                'first': '<i class="fa fa-angle-double-left"></i>',
                'previous': '<i class="fa fa-angle-left" ></i>',
                'next': '<i class="fa fa-angle-right" ></i>',
                'last': '<i class="fa fa-angle-double-right"></i>'
            },
        },
        ajax: {
            url: "{{ route('live-room-ex-diamond', $token) }}",
        },
        order: [],
        "columns":[
            {"data": "room_name" },
            {"data": "room_url" },
            {"data": "external_link", class: 'text-center' },
            {"data": "is_active", class: 'text-center' },
            {"data": "is_used", class: 'text-center' },
            {"data": "used_count", class: 'text-center' },
            {"data": "created_at", class: 'text-center' },
            {"data": "action", orderable: false}
        ],
    });

    $(document).on('click', '.add_room', function(e) {
        e.preventDefault();
        $('div.room_modal').load($(this).attr('data-href'), function() {
            $(this).modal('show');
        });
    });

     // toggle active/inactive
     $(document).on('click', '.toggle-btn', function(e) {
        e.preventDefault();
        var url = $(this).data('href');
        $.ajax({
            method: "GET",
            url: url,
            dataType: "json",
            success: function(result) {
                if (result.success == true) {
                    toastr.options = {
                        "progressBar": true,
                        "timeOut": "2000",
                    }
                    toastr.success(result.message);
                }
                room_table.ajax.reload();
            },
            error: function(xhr) {
                toastr.error('Có lỗi xảy ra!');
            }
        });
    });

    // Initialize History DataTable
    var history_table = $('#history_table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "{{ route('live-room-ex-diamond.history', $token) }}",
        },
        order: [[5, 'desc']], // Order by distribution time desc
        "columns":[
            {"data": "room_info", orderable: false },
            {"data": "self_uid", class: 'text-center' },
            {"data": "diamond_balance", class: 'text-center' },
            {"data": "status", class: 'text-center' },
            {"data": "points_used", class: 'text-center' },
            {"data": "created_at", class: 'text-center' },
            {"data": "success_at", class: 'text-center' }
        ],
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Vietnamese.json"
        },
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]]
    });
</script>
@endsection
