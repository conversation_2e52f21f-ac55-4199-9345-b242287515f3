<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDailyStatsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('daily_stats', function (Blueprint $table) {
            $table->id();
            $table->date('stat_date')->unique()->comment('Ngày thống kê');
            
            // Số lượng UID thay đổi
            $table->integer('uid_changes_count')->default(0)->comment('Số lượng UID có thay đổi điểm');
            
            // Số lần tăng, giảm
            $table->integer('increase_count')->default(0)->comment('Số lần tăng điểm');
            $table->integer('decrease_count')->default(0)->comment('Số lần giảm điểm');
            
            // Tổng lượng tăng, giảm của diamond và bean
            $table->bigInteger('total_diamond_increase')->default(0)->comment('Tổng lượng diamond tăng');
            $table->bigInteger('total_diamond_decrease')->default(0)->comment('Tổng lượng diamond giảm');
            $table->bigInteger('total_bean_increase')->default(0)->comment('Tổng lượng bean tăng');
            $table->bigInteger('total_bean_decrease')->default(0)->comment('Tổng lượng bean giảm');
            
            // Tổng diamond, bean theo ngày (từ các UID có thay đổi)
            $table->bigInteger('total_diamond')->default(0)->comment('Tổng diamond của các UID có thay đổi');
            $table->bigInteger('total_bean')->default(0)->comment('Tổng bean của các UID có thay đổi');
            
            // Thông tin bổ sung
            $table->integer('unique_users_count')->default(0)->comment('Số lượng user duy nhất có thay đổi');
            $table->json('calculation_metadata')->nullable()->comment('Metadata về quá trình tính toán');
            
            $table->timestamps();

            // Indexes để tối ưu query
            $table->index('stat_date');
            $table->index(['stat_date', 'uid_changes_count']);
            $table->index(['stat_date', 'total_diamond']);
            $table->index(['stat_date', 'total_bean']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('daily_stats');
    }
}
