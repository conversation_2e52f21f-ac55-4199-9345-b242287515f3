<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class DailyStat extends Model
{
    use HasFactory;

    protected $table = 'daily_stats';

    protected $fillable = [
        'stat_date',
        'uid_changes_count',
        'increase_count',
        'decrease_count',
        'total_diamond_increase',
        'total_diamond_decrease',
        'total_bean_increase',
        'total_bean_decrease',
        'total_diamond',
        'total_bean',
        'unique_users_count',
        'calculation_metadata',
    ];

    protected $casts = [
        'stat_date' => 'date',
        'uid_changes_count' => 'integer',
        'increase_count' => 'integer',
        'decrease_count' => 'integer',
        'total_diamond_increase' => 'integer',
        'total_diamond_decrease' => 'integer',
        'total_bean_increase' => 'integer',
        'total_bean_decrease' => 'integer',
        'total_diamond' => 'integer',
        'total_bean' => 'integer',
        'unique_users_count' => 'integer',
        'calculation_metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Scope để lấy thống kê trong khoảng thời gian
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('stat_date', [$startDate, $endDate]);
    }

    /**
     * Scope để lấy thống kê cho ngày cụ thể
     */
    public function scopeForDate($query, $date)
    {
        return $query->where('stat_date', $date);
    }

    /**
     * Scope để lấy thống kê gần đây nhất
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('stat_date', '>=', Carbon::now()->subDays($days)->format('Y-m-d'))
                    ->orderBy('stat_date', 'desc');
    }

    /**
     * Lấy thống kê cho khoảng thời gian
     */
    public static function getStatsForDateRange($startDate, $endDate)
    {
        return static::inDateRange($startDate, $endDate)
                    ->orderBy('stat_date', 'desc')
                    ->get();
    }

    /**
     * Lấy thống kê cho ngày cụ thể
     */
    public static function getStatsForDate($date)
    {
        return static::forDate($date)->first();
    }

    /**
     * Lấy thống kê gần đây
     */
    public static function getRecentStats($days = 7)
    {
        return static::recent($days)->get();
    }

    /**
     * Format diamond increase
     */
    public function getFormattedDiamondIncreaseAttribute()
    {
        return number_format($this->total_diamond_increase);
    }

    /**
     * Format diamond decrease
     */
    public function getFormattedDiamondDecreaseAttribute()
    {
        return number_format($this->total_diamond_decrease);
    }

    /**
     * Format bean increase
     */
    public function getFormattedBeanIncreaseAttribute()
    {
        return number_format($this->total_bean_increase);
    }

    /**
     * Format bean decrease
     */
    public function getFormattedBeanDecreaseAttribute()
    {
        return number_format($this->total_bean_decrease);
    }

    /**
     * Format total diamond
     */
    public function getFormattedTotalDiamondAttribute()
    {
        return number_format($this->total_diamond);
    }

    /**
     * Format total bean
     */
    public function getFormattedTotalBeanAttribute()
    {
        return number_format($this->total_bean);
    }

    /**
     * Tính tổng dao động diamond (tăng + giảm)
     */
    public function getTotalDiamondFluctuationAttribute()
    {
        return $this->total_diamond_increase + $this->total_diamond_decrease;
    }

    /**
     * Tính tổng dao động bean (tăng + giảm)
     */
    public function getTotalBeanFluctuationAttribute()
    {
        return $this->total_bean_increase + $this->total_bean_decrease;
    }

    /**
     * Format tổng dao động diamond
     */
    public function getFormattedTotalDiamondFluctuationAttribute()
    {
        return number_format($this->total_diamond_fluctuation);
    }

    /**
     * Format tổng dao động bean
     */
    public function getFormattedTotalBeanFluctuationAttribute()
    {
        return number_format($this->total_bean_fluctuation);
    }
}
