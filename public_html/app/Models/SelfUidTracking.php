<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class SelfUidTracking extends Model
{
    use HasFactory;

    protected $table = 'self_uid_trackings';

    protected $fillable = [
        'user_token',
        'user_id',
        'self_uid',
        'diamond_balance',
        'bean_balance',
        'room_count',
        'device_name',
        'raw_self_id',
        'ip_address',
        'user_agent',
        'is_old_client',
        'client_notes'
    ];

    protected $casts = [
        'diamond_balance' => 'integer',
        'bean_balance' => 'integer',
        'room_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    // Scope để lấy tracking theo user_token
    public function scopeByUserToken($query, $userToken)
    {
        return $query->where('user_token', $userToken);
    }

    // Scope để lấy tracking theo user_id
    public function scopeByUserId($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    // Scope để lấy tracking theo self_uid
    public function scopeBySelfUid($query, $selfUid)
    {
        return $query->where('self_uid', $selfUid);
    }

    // Scope để lấy tracking trong khoảng thời gian
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    // Method để parse selfId từ format: "selfUid_diamond_bean_roomCount_deviceName"
    // Hỗ trợ client cũ có thể thiếu roomCount hoặc deviceName: "selfUid_diamond_bean"
    public static function parseSelfId($selfId)
    {
        $parts = explode('_', $selfId);

        if (count($parts) >= 4) {
            return [
                'self_uid' => $parts[0],
                'diamond_balance' => (int)$parts[1],
                'bean_balance' => (int)$parts[2],
                'room_count' => isset($parts[3]) ? (int)$parts[3] : 0, // Mặc định 0 nếu thiếu
                'device_name' => isset($parts[4]) ? $parts[4] : null
            ];
        } elseif (count($parts) >= 3) {
            return [
                'self_uid' => $parts[0],
                'diamond_balance' => (int)$parts[1],
                'bean_balance' => (int)$parts[2],
                'room_count' => isset($parts[3]) ? (int)$parts[3] : 0, // Mặc định 0 nếu thiếu
                'device_name' => null
            ];
        } elseif (count($parts) >= 2) {
            // Trường hợp chỉ có selfUid_diamond
            return [
                'self_uid' => $parts[0],
                'diamond_balance' => (int)$parts[1],
                'bean_balance' => 0, // Mặc định 0 nếu thiếu
                'room_count' => 0,    // Mặc định 0 nếu thiếu
                'device_name' => null
            ];
        } elseif (count($parts) >= 1) {
            // Trường hợp chỉ có selfUid
            return [
                'self_uid' => $parts[0],
                'diamond_balance' => 0, // Mặc định 0 nếu thiếu
                'bean_balance' => 0,    // Mặc định 0 nếu thiếu
                'room_count' => 0,       // Mặc định 0 nếu thiếu
                'device_name' => null
            ];
        }

        return null;
    }

    // Method để lấy lịch sử thay đổi của một selfUid
    public static function getHistoryBySelfUid($selfUid, $limit = 50)
    {
        return self::where('self_uid', $selfUid)
                   ->orderBy('created_at', 'desc')
                   ->limit($limit)
                   ->get();
    }

    // Method để lấy thống kê tổng quan
    public static function getStatsByUserToken($userToken)
    {
        return self::where('user_token', $userToken)
                   ->selectRaw('
                       COUNT(DISTINCT self_uid) as unique_users,
                       COUNT(*) as total_requests,
                       MAX(created_at) as last_activity
                   ')
                   ->first();
    }

    // Method để lấy bản ghi cuối cùng của một selfUid
    public static function getLastRecord($userToken, $selfUid)
    {
        return self::where('user_token', $userToken)
                   ->where('self_uid', $selfUid)
                   ->orderBy('created_at', 'desc')
                   ->first();
    }

    // Method để kiểm tra có thay đổi balance không
    public static function hasBalanceChanges($userToken, $selfUid, $newDiamond, $newBean, $newRoomCount)
    {
        $lastRecord = self::getLastRecord($userToken, $selfUid);

        if (!$lastRecord) {
            return true; // Nếu chưa có bản ghi nào thì coi như có thay đổi
        }

        // So sánh các giá trị, xử lý trường hợp null
        $diamondChanged = $lastRecord->diamond_balance != $newDiamond;
        $beanChanged = $lastRecord->bean_balance != $newBean;
        // $roomCountChanged = $lastRecord->room_count != $newRoomCount;

        return ($diamondChanged || $beanChanged);
        // return ($diamondChanged || $beanChanged || ($roomCountChanged && $newRoomCount >= 0));
    }

    // Method để tính tổng số diamond và bean đã giảm của một selfUid
    public static function getTotalDecrease($userToken, $selfUid)
    {
        $records = self::where('user_token', $userToken)
                      ->where('self_uid', $selfUid)
                      ->orderBy('created_at', 'asc')
                      ->get();

        if ($records->count() < 2) {
            return [
                'total_diamond_decrease' => 0,
                'total_bean_decrease' => 0,
                'decrease_count' => 0
            ];
        }

        $totalDiamondDecrease = 0;
        $totalBeanDecrease = 0;
        $decreaseCount = 0;

        for ($i = 1; $i < $records->count(); $i++) {
            $previous = $records[$i - 1];
            $current = $records[$i];

            // Tính sự giảm (chỉ tính khi giảm, không tính khi tăng)
            $diamondDecrease = max(0, $previous->diamond_balance - $current->diamond_balance);
            $beanDecrease = max(0, $previous->bean_balance - $current->bean_balance);

            if ($diamondDecrease > 0 || $beanDecrease > 0) {
                $totalDiamondDecrease += $diamondDecrease;
                $totalBeanDecrease += $beanDecrease;
                $decreaseCount++;
            }
        }

        return [
            'total_diamond_decrease' => $totalDiamondDecrease,
            'total_bean_decrease' => $totalBeanDecrease,
            'decrease_count' => $decreaseCount,
            'records_analyzed' => $records->count()
        ];
    }

    // Method để lấy tổng giảm của tất cả selfUid theo userToken
    public static function getTotalDecreaseByUserToken($userToken)
    {
        $selfUids = self::where('user_token', $userToken)
                       ->distinct()
                       ->pluck('self_uid');

        $totalDiamondDecrease = 0;
        $totalBeanDecrease = 0;
        $totalDecreaseCount = 0;
        $uidDetails = [];

        foreach ($selfUids as $selfUid) {
            $decrease = self::getTotalDecrease($userToken, $selfUid);

            if ($decrease['total_diamond_decrease'] > 0 || $decrease['total_bean_decrease'] > 0) {
                $totalDiamondDecrease += $decrease['total_diamond_decrease'];
                $totalBeanDecrease += $decrease['total_bean_decrease'];
                $totalDecreaseCount += $decrease['decrease_count'];

                $uidDetails[] = [
                    'self_uid' => $selfUid,
                    'diamond_decrease' => $decrease['total_diamond_decrease'],
                    'bean_decrease' => $decrease['total_bean_decrease'],
                    'decrease_count' => $decrease['decrease_count']
                ];
            }
        }

        return [
            'total_diamond_decrease' => $totalDiamondDecrease,
            'total_bean_decrease' => $totalBeanDecrease,
            'total_decrease_count' => $totalDecreaseCount,
            'uid_count' => count($uidDetails),
            'uid_details' => $uidDetails
        ];
    }

    // Method để tính tổng số diamond và bean đã tăng của một selfUid
    public static function getTotalIncrease($userToken, $selfUid)
    {
        $records = self::where('user_token', $userToken)
                      ->where('self_uid', $selfUid)
                      ->orderBy('created_at', 'asc')
                      ->get();

        if ($records->count() < 2) {
            return [
                'total_diamond_increase' => 0,
                'total_bean_increase' => 0,
                'increase_count' => 0
            ];
        }

        $totalDiamondIncrease = 0;
        $totalBeanIncrease = 0;
        $increaseCount = 0;

        for ($i = 1; $i < $records->count(); $i++) {
            $previous = $records[$i - 1];
            $current = $records[$i];

            // Tính sự tăng (chỉ tính khi tăng, không tính khi giảm)
            $diamondIncrease = max(0, $current->diamond_balance - $previous->diamond_balance);
            $beanIncrease = max(0, $current->bean_balance - $previous->bean_balance);

            if ($diamondIncrease > 0 || $beanIncrease > 0) {
                $totalDiamondIncrease += $diamondIncrease;
                $totalBeanIncrease += $beanIncrease;
                $increaseCount++;
            }
        }

        return [
            'total_diamond_increase' => $totalDiamondIncrease,
            'total_bean_increase' => $totalBeanIncrease,
            'increase_count' => $increaseCount,
            'records_analyzed' => $records->count()
        ];
    }

    // Method để lấy tổng tăng của tất cả selfUid theo userToken
    public static function getTotalIncreaseByUserToken($userToken)
    {
        $selfUids = self::where('user_token', $userToken)
                       ->distinct()
                       ->pluck('self_uid');

        $totalDiamondIncrease = 0;
        $totalBeanIncrease = 0;
        $totalIncreaseCount = 0;
        $uidDetails = [];

        foreach ($selfUids as $selfUid) {
            $increase = self::getTotalIncrease($userToken, $selfUid);

            if ($increase['total_diamond_increase'] > 0 || $increase['total_bean_increase'] > 0) {
                $totalDiamondIncrease += $increase['total_diamond_increase'];
                $totalBeanIncrease += $increase['total_bean_increase'];
                $totalIncreaseCount += $increase['increase_count'];

                $uidDetails[] = [
                    'self_uid' => $selfUid,
                    'diamond_increase' => $increase['total_diamond_increase'],
                    'bean_increase' => $increase['total_bean_increase'],
                    'increase_count' => $increase['increase_count']
                ];
            }
        }

        return [
            'total_diamond_increase' => $totalDiamondIncrease,
            'total_bean_increase' => $totalBeanIncrease,
            'total_increase_count' => $totalIncreaseCount,
            'uid_count' => count($uidDetails),
            'uid_details' => $uidDetails
        ];
    }

    // Method để tạo tracking record với kiểm tra thay đổi
    public static function createIfChanged($userToken, $parsedData, $rawSelfId, $ipAddress, $userAgent, $isOldClient = false)
    {
        // Lấy user_id từ user_token
        $user = \App\Models\User::where('user_token', $userToken)->first();
        $userId = $user ? $user->id : null;
        
        // Cập nhật bảng self_uids requested_at
        $selfUidRecord = \App\Models\SelfUid::where('self_uid', $parsedData['self_uid'])->first();
        if ($selfUidRecord) {
            // Tắt tính năng tự động cập nhật timestamps (bao gồm updated_at) cho instance này
            $selfUidRecord->timestamps = false;

            // Cập nhật chỉ trường requested_at
            $selfUidRecord->update([
                'requested_at' => Carbon::now(),
                'device_name' => $parsedData['device_name'],
            ]);
            $selfUidRecord->timestamps = true;
        }
        
        // Kiểm tra xem có phải bản ghi đầu tiên của UID này không
        $isFirstRecord = !self::where('user_token', $userToken)
                              ->where('self_uid', $parsedData['self_uid'])
                              ->exists();

        // Không ghi nhận nếu cả diamond và bean đều bằng 0, room_count -1
        if ($parsedData['diamond_balance'] == 0 && $parsedData['bean_balance'] == 0 && $parsedData['room_count'] == -1) {
            return null;
        }

        if (self::hasBalanceChanges(
            $userToken,
            $parsedData['self_uid'],
            $parsedData['diamond_balance'],
            $parsedData['bean_balance'],
            $parsedData['room_count']
        ) || $isFirstRecord) {
            // Tạo client notes dựa trên format selfId
            $clientNotes = null;
            if ($isOldClient) {
                $parts = explode('_', $rawSelfId);
                if (count($parts) == 4) {
                    $clientNotes = 'Client cũ - thiếu roomCount và deviceName';
                } elseif (count($parts) == 3) {
                    $clientNotes = 'Client cũ - thiếu deviceName';
                } elseif (count($parts) == 2) {
                    $clientNotes = 'Client cũ - thiếu bean, roomCount và deviceName';
                } elseif (count($parts) == 1) {
                    $clientNotes = 'Client cũ - chỉ có selfUid';
                }
            }

            // Tạo bản ghi tracking
            $trackingRecord = self::create([
                'user_token' => $userToken,
                'user_id' => $userId,
                'self_uid' => $parsedData['self_uid'],
                'diamond_balance' => $parsedData['diamond_balance'],
                'bean_balance' => $parsedData['bean_balance'],
                'room_count' => $parsedData['room_count'],
                'raw_self_id' => $rawSelfId,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'is_old_client' => $isOldClient,
                'client_notes' => $clientNotes,
                'device_name' => $parsedData['device_name']
            ]);

            // Cập nhật bảng self_uids
            if ($trackingRecord) {
                \App\Models\SelfUid::updateOrCreate(
                    ['self_uid' => $parsedData['self_uid']],
                    [
                        'user_token' => $userToken,
                        'user_id' => $userId,
                        'diamond_balance' => $parsedData['diamond_balance'],
                        'bean_balance' => $parsedData['bean_balance'],
                        'ip_address' => $ipAddress,
                        'device_name' => $parsedData['device_name'],
                    ]
                );
            }

            return $trackingRecord;
        }

        return null; // Không có thay đổi, không tạo record
    }
}
