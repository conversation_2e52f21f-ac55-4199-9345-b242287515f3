<?php

namespace App\Services;

use App\Models\DailyStat;
use App\Models\SelfUidTracking;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class DailyStatsService
{
    /**
     * Tạo thống kê cho ngày cụ thể
     */
    public function generateStatsForDate($targetDate, $force = false)
    {
        $targetDate = Carbon::parse($targetDate)->format('Y-m-d');
        
        try {
            // Kiểm tra dữ liệu hiện có
            $existingStat = DailyStat::where('stat_date', $targetDate)->first();
            
            if ($existingStat && !$force) {
                return [
                    'success' => false,
                    'message' => "Đã có thống kê cho ngày {$targetDate}. Sử dụng force=true để ghi đè.",
                    'existing_stat' => $existingStat
                ];
            }

            // Tính toán thống kê
            $stats = $this->calculateStatsForDate($targetDate);
            
            if (!$stats) {
                return [
                    'success' => true,
                    'message' => "Không có hoạt động nào cho ngày {$targetDate}",
                    'stats' => null
                ];
            }

            // Lưu hoặc cập nhật thống kê
            $dailyStat = DailyStat::updateOrCreate(
                ['stat_date' => $targetDate],
                $stats
            );

            return [
                'success' => true,
                'message' => "Đã tạo thống kê cho ngày {$targetDate}",
                'stats' => $dailyStat
            ];

        } catch (\Exception $e) {
            Log::error("Lỗi khi tạo thống kê cho ngày {$targetDate}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => "Lỗi: " . $e->getMessage(),
                'stats' => null
            ];
        }
    }

    /**
     * Tính toán thống kê cho ngày cụ thể
     */
    private function calculateStatsForDate($targetDate)
    {
        $startOfDay = Carbon::parse($targetDate)->startOfDay();
        $endOfDay = Carbon::parse($targetDate)->endOfDay();

        // Lấy tất cả tracking records trong ngày, sắp xếp theo self_uid và thời gian
        $trackingRecords = SelfUidTracking::whereBetween('created_at', [$startOfDay, $endOfDay])
            ->orderBy('self_uid')
            ->orderBy('created_at')
            ->get();

        if ($trackingRecords->isEmpty()) {
            return null;
        }

        // Group theo self_uid
        $groupedBySelfUid = $trackingRecords->groupBy('self_uid');
        
        $stats = [
            'uid_changes_count' => 0,
            'increase_count' => 0,
            'decrease_count' => 0,
            'total_diamond_increase' => 0,
            'total_diamond_decrease' => 0,
            'total_bean_increase' => 0,
            'total_bean_decrease' => 0,
            'total_diamond' => 0,
            'total_bean' => 0,
            'unique_users_count' => 0,
            'calculation_metadata' => []
        ];

        $uniqueUsers = collect();
        $uidsWithChanges = 0;

        foreach ($groupedBySelfUid as $selfUid => $selfUidRecords) {
            $uidStats = $this->calculateUidStats($selfUidRecords);
            
            // Nếu có thay đổi điểm
            if ($uidStats['has_changes']) {
                $uidsWithChanges++;
                
                $stats['increase_count'] += $uidStats['increase_count'];
                $stats['decrease_count'] += $uidStats['decrease_count'];
                $stats['total_diamond_increase'] += $uidStats['diamond_increase'];
                $stats['total_diamond_decrease'] += $uidStats['diamond_decrease'];
                $stats['total_bean_increase'] += $uidStats['bean_increase'];
                $stats['total_bean_decrease'] += $uidStats['bean_decrease'];
                
                // Lấy số dư cuối ngày
                $lastRecord = $selfUidRecords->last();
                $stats['total_diamond'] += $lastRecord->diamond_balance;
                $stats['total_bean'] += $lastRecord->bean_balance;
                
                // Thu thập unique users
                if ($lastRecord->user_token) {
                    $uniqueUsers->push($lastRecord->user_token);
                }
            }
        }

        $stats['uid_changes_count'] = $uidsWithChanges;
        $stats['unique_users_count'] = $uniqueUsers->unique()->count();
        $stats['calculation_metadata'] = [
            'total_tracking_records' => $trackingRecords->count(),
            'total_unique_uids' => $groupedBySelfUid->count(),
            'calculated_at' => Carbon::now()->toISOString()
        ];

        return $stats;
    }

    /**
     * Tính toán thống kê cho một UID cụ thể
     */
    private function calculateUidStats($records)
    {
        $stats = [
            'has_changes' => false,
            'increase_count' => 0,
            'decrease_count' => 0,
            'diamond_increase' => 0,
            'diamond_decrease' => 0,
            'bean_increase' => 0,
            'bean_decrease' => 0,
        ];

        $previousRecord = null;

        foreach ($records as $record) {
            if ($previousRecord) {
                $diamondChange = $record->diamond_balance - $previousRecord->diamond_balance;
                $beanChange = $record->bean_balance - $previousRecord->bean_balance;

                // Kiểm tra thay đổi diamond
                if ($diamondChange > 0) {
                    $stats['diamond_increase'] += $diamondChange;
                    $stats['increase_count']++;
                    $stats['has_changes'] = true;
                } elseif ($diamondChange < 0) {
                    $stats['diamond_decrease'] += abs($diamondChange);
                    $stats['decrease_count']++;
                    $stats['has_changes'] = true;
                }

                // Kiểm tra thay đổi bean
                if ($beanChange > 0) {
                    $stats['bean_increase'] += $beanChange;
                    if ($diamondChange == 0) { // Chỉ đếm nếu diamond không thay đổi
                        $stats['increase_count']++;
                    }
                    $stats['has_changes'] = true;
                } elseif ($beanChange < 0) {
                    $stats['bean_decrease'] += abs($beanChange);
                    if ($diamondChange == 0) { // Chỉ đếm nếu diamond không thay đổi
                        $stats['decrease_count']++;
                    }
                    $stats['has_changes'] = true;
                }
            }
            $previousRecord = $record;
        }

        return $stats;
    }

    /**
     * Xóa thống kê cho ngày cụ thể
     */
    public function clearStatsForDate($date)
    {
        $targetDate = Carbon::parse($date)->format('Y-m-d');
        $deletedCount = DailyStat::where('stat_date', $targetDate)->delete();

        Log::info("Đã xóa thống kê cho ngày {$targetDate}");

        return $deletedCount;
    }

    /**
     * Kiểm tra tính toàn vẹn dữ liệu cho một ngày
     */
    public function validateStatsForDate($date)
    {
        $targetDate = Carbon::parse($date)->format('Y-m-d');
        $startOfDay = Carbon::parse($targetDate)->startOfDay();
        $endOfDay = Carbon::parse($targetDate)->endOfDay();

        // Kiểm tra xem có thống kê trong bảng daily_stats không
        $dailyStat = DailyStat::where('stat_date', $targetDate)->first();

        // Đếm số tracking records trong ngày
        $trackingCount = SelfUidTracking::whereBetween('created_at', [$startOfDay, $endOfDay])->count();

        return [
            'date' => $targetDate,
            'has_daily_stat' => $dailyStat !== null,
            'tracking_records_count' => $trackingCount,
            'daily_stat' => $dailyStat,
            'is_complete' => $dailyStat !== null && $trackingCount > 0
        ];
    }

    /**
     * Lấy tóm tắt thống kê cho ngày
     */
    public function getDailyStatsSummary($date)
    {
        $dailyStat = DailyStat::where('stat_date', $date)->first();

        if (!$dailyStat) {
            return [
                'date' => $date,
                'exists' => false,
                'uid_changes_count' => 0,
                'total_diamond' => 0,
                'total_bean' => 0,
                'unique_users_count' => 0
            ];
        }

        return [
            'date' => $date,
            'exists' => true,
            'uid_changes_count' => $dailyStat->uid_changes_count,
            'total_diamond' => $dailyStat->total_diamond,
            'total_bean' => $dailyStat->total_bean,
            'unique_users_count' => $dailyStat->unique_users_count,
            'increase_count' => $dailyStat->increase_count,
            'decrease_count' => $dailyStat->decrease_count,
            'total_diamond_increase' => $dailyStat->total_diamond_increase,
            'total_diamond_decrease' => $dailyStat->total_diamond_decrease,
            'total_bean_increase' => $dailyStat->total_bean_increase,
            'total_bean_decrease' => $dailyStat->total_bean_decrease,
        ];
    }
}
