<?php

namespace App\Http\Controllers;

use App\Models\SelfUidTracking;
use App\Models\SelfUid;
use App\Models\User;
use App\Models\DailySelfUidStat;
use App\Models\DailyStat;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;

class SelfUidTrackingController extends Controller
{
    /**
     * Áp dụng filter cho query
     */
    private function applyFilters($query, Request $request)
    {
        if ($request->filled('user_token')) {
            $query->where('user_token', $request->user_token);
        }

        if ($request->filled('self_uid')) {
            $query->where('self_uid', 'LIKE', '%' . $request->self_uid . '%');
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        return $query;
    }

    /**
     * Áp dụng filter cho SelfUid query
     */
    private function applySelfUidFilters($query, Request $request)
    {
        if ($request->filled('user_token')) {
            // Tìm user_id từ user_token để filter hiệu quả hơn
            $user = User::where('user_token', $request->user_token)->first();
            if ($user) {
                $query->where('user_id', $user->id);
            } else {
                // Nếu không tìm thấy user, trả về empty result
                $query->whereRaw('1 = 0');
            }
        }

        if ($request->filled('self_uid')) {
            $query->where('self_uid', 'LIKE', '%' . $request->self_uid . '%');
        }

        if ($request->filled('ip_address')) {
            $query->where('ip_address', 'LIKE', '%' . $request->ip_address . '%');
        }

        if ($request->filled('date_from')) {
            $query->whereDate('updated_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('updated_at', '<=', $request->date_to);
        }

        return $query;
    }

    /**
     * Áp dụng sorting cho SelfUid query
     */
    private function applySelfUidSorting($query, Request $request)
    {
        $sortBy = $request->get('sort_by', 'last_diamond_change');
        $sortDirection = $request->get('sort_direction', 'desc');

        // Validate sort direction
        if (!in_array($sortDirection, ['asc', 'desc'])) {
            $sortDirection = 'desc';
        }

        // Validate sort field
        $allowedSortFields = ['updated_at', 'diamond_balance', 'bean_balance', 'ip_address', 'self_uid', 'device_name', 'last_diamond_change'];
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'last_diamond_change';
        }

        // Xử lý sắp xếp đặc biệt cho last_diamond_change
        if ($sortBy === 'last_diamond_change') {
            // Sử dụng subquery để lấy thời điểm thay đổi diamond_balance cuối cùng
            $subquery = SelfUidTracking::selectRaw('MAX(created_at)')
                ->whereColumn('self_uid', 'self_uids.self_uid')
                ->where('created_at', '>=', Carbon::now()->subHours(48));

            return $query->orderByRaw(
                "COALESCE(({$subquery->toSql()}), '1900-01-01') {$sortDirection}",
                $subquery->getBindings()
            );
        }

        return $query->orderBy($sortBy, $sortDirection);
    }

    /**
     * Thêm thông tin thời điểm thay đổi diamond_balance cuối cùng cho mỗi SelfUid
     */
    private function addLastDiamondChangeTime($selfUids)
    {
        $selfUidList = $selfUids->pluck('self_uid')->toArray();

        if (empty($selfUidList)) {
            return;
        }

        // Lấy thời điểm thay đổi diamond_balance cuối cùng cho tất cả self_uid
        $lastChanges = [];
        $cutoffTime = Carbon::now()->subHours(48);

        foreach ($selfUidList as $selfUid) {
            $lastChangeTime = $this->getLastDiamondChangeTimeForSelfUid($selfUid, $cutoffTime);
            $lastChanges[$selfUid] = $lastChangeTime;
        }

        // Thêm thông tin vào từng item
        foreach ($selfUids as $selfUid) {
            $selfUid->last_diamond_change = $lastChanges[$selfUid->self_uid];
        }
    }

    /**
     * Lấy thời điểm cuối cùng mà diamond_balance thực sự thay đổi cho một SelfUid
     */
    private function getLastDiamondChangeTimeForSelfUid($selfUid, $cutoffTime)
    {
        // Lấy tất cả tracking records trong 48h qua cho self_uid này
        $trackings = SelfUidTracking::where('self_uid', $selfUid)
            ->where('created_at', '>=', $cutoffTime)
            ->orderBy('created_at')
            ->get();

        if ($trackings->isEmpty()) {
            return null;
        }

        $lastChangeTime = null;
        $previousBalance = null;

        foreach ($trackings as $record) {
            // Nếu đây là bản ghi đầu tiên hoặc diamond_balance có thay đổi
            if ($previousBalance === null || $record->diamond_balance != $previousBalance) {
                $lastChangeTime = $record->created_at;
            }
            $previousBalance = $record->diamond_balance;
        }

        return $lastChangeTime;
    }

    /**
     * Thêm thông tin thời điểm thay đổi diamond_balance cuối cùng cho collection
     */
    private function addLastDiamondChangeTimeToCollection($collection)
    {
        $selfUidList = $collection->pluck('self_uid')->toArray();

        if (empty($selfUidList)) {
            return;
        }

        // Lấy thời điểm thay đổi diamond_balance cuối cùng cho tất cả self_uid
        $lastChanges = [];
        $cutoffTime = Carbon::now()->subHours(48);

        foreach ($selfUidList as $selfUid) {
            $lastChangeTime = $this->getLastDiamondChangeTimeForSelfUid($selfUid, $cutoffTime);
            $lastChanges[$selfUid] = $lastChangeTime;
        }

        // Thêm thông tin vào từng item
        foreach ($collection as $selfUid) {
            $selfUid->last_diamond_change = $lastChanges[$selfUid->self_uid];
        }
    }

    /**
     * Tạo pagination thủ công cho collection
     */
    private function paginateCollection($collection, $perPage, $currentPage)
    {
        $currentPage = max(1, (int) $currentPage);
        $total = $collection->count();
        $offset = ($currentPage - 1) * $perPage;
        $items = $collection->slice($offset, $perPage)->values();

        return new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $currentPage,
            [
                'path' => request()->url(),
                'pageName' => 'page',
                'query' => request()->except('page')
            ]
        );
    }

    /**
     * Lấy thống kê tổng quan từ bảng SelfUid
     */
    private function getSelfUidOverallStats(Request $request)
    {
        $query = SelfUid::query();
        $filteredQuery = $this->applySelfUidFilters($query, $request);

        // Lọc các bản ghi có requested_at trong ngày hiện tại
        $recentRequestedSelfUidsCount = (clone $filteredQuery)
            ->where('requested_at', '>=', Carbon::today())
            ->count();

        return [
            'uniqueSelfUidsCount' => $filteredQuery->count(),
            'totalDiamond' => $filteredQuery->sum('diamond_balance'),
            'totalBean' => $filteredQuery->sum('bean_balance'),
            'recentRequestedSelfUidsCount' => $recentRequestedSelfUidsCount,
        ];
    }

    /**
     * Lấy thống kê tổng quan
     */
    private function getOverallStats(Request $request)
    {
        // Lấy bản ghi mới nhất của mỗi Self UID trong phạm vi filter
        $latestRecordsQuery = SelfUidTracking::select('self_uid', 'diamond_balance', 'bean_balance', 'created_at')
            ->whereIn('id', function($query) use ($request) {
                $subQuery = $query->select(DB::raw('MAX(id)'))
                    ->from('self_uid_trackings as st')
                    ->groupBy('st.self_uid');

                $this->applyFilters($subQuery, $request);
            });

        $latestRecords = $this->applyFilters($latestRecordsQuery, $request)->get();

        return [
            'uniqueSelfUidsCount' => $latestRecords->count(),
            'totalDiamond' => $latestRecords->sum('diamond_balance'),
            'totalBean' => $latestRecords->sum('bean_balance')
        ];
    }



    /**
     * Lấy thống kê theo ngày - sử dụng bảng daily_stats mới
     */
    private function getDailyStats(Request $request)
    {
        // Lấy thống kê từ bảng daily_stats (7 ngày gần nhất)
        $dailyStats = DailyStat::orderBy('stat_date', 'desc')
            ->limit(7)
            ->get();

        // Nếu không có dữ liệu, trả về collection rỗng
        if ($dailyStats->isEmpty()) {
            return collect([]);
        }

        // Chuyển đổi format để tương thích với view hiện tại
        $formattedStats = $dailyStats->map(function($stat) {
            return (object) [
                'date' => $stat->stat_date, // Đảm bảo date được set đúng từ stat_date
                'unique_self_uids' => $stat->uid_changes_count, // Số UID có thay đổi
                'total_diamond' => $stat->total_diamond,
                'total_bean' => $stat->total_bean,
                'diamond_change' => $stat->total_diamond_increase + $stat->total_diamond_decrease, // Tổng dao động
                'bean_change' => $stat->total_bean_increase + $stat->total_bean_decrease, // Tổng dao động
                'total_records' => $stat->uid_changes_count,
                // Thêm các thông tin mới
                'increase_count' => $stat->increase_count,
                'decrease_count' => $stat->decrease_count,
                'total_diamond_increase' => $stat->total_diamond_increase,
                'total_diamond_decrease' => $stat->total_diamond_decrease,
                'total_bean_increase' => $stat->total_bean_increase,
                'total_bean_decrease' => $stat->total_bean_decrease,
                'unique_users_count' => $stat->unique_users_count,
            ];
        });

        return $formattedStats;
    }

    /**
     * Lấy thống kê cho ngày hôm nay bằng logic cũ
     */
    private function getTodayStats(Request $request)
    {
        $today = Carbon::now()->format('Y-m-d');
        
        // Lấy bản ghi mới nhất của mỗi Self UID trong ngày hôm nay
        $latestRecordsQuery = SelfUidTracking::select('self_uid', 'diamond_balance', 'bean_balance')
            ->whereIn('id', function($query) use ($request, $today) {
                $subQuery = $query->select(DB::raw('MAX(id)'))
                    ->from('self_uid_trackings as st')
                    ->whereDate('st.created_at', $today)
                    ->groupBy('st.self_uid');

                // Áp dụng filter cho subquery (trừ date filter vì đã có whereDate ở trên)
                if ($request->filled('user_token')) {
                    $subQuery->where('st.user_token', $request->user_token);
                }
                if ($request->filled('self_uid')) {
                    $subQuery->where('st.self_uid', 'LIKE', '%' . $request->self_uid . '%');
                }
            })
            ->whereDate('created_at', $today);

        // Áp dụng filter cho query chính (trừ date filter)
        if ($request->filled('user_token')) {
            $latestRecordsQuery->where('user_token', $request->user_token);
        }
        if ($request->filled('self_uid')) {
            $latestRecordsQuery->where('self_uid', 'LIKE', '%' . $request->self_uid . '%');
        }

        $latestRecords = $latestRecordsQuery->get();

        if ($latestRecords->isEmpty()) {
            return null;
        }

        $currentDiamond = $latestRecords->sum('diamond_balance');
        $currentBean = $latestRecords->sum('bean_balance');

        return (object) [
            'date' => $today,
            'unique_self_uids' => $latestRecords->count(),
            'total_diamond' => $currentDiamond,
            'total_bean' => $currentBean,
            'total_records' => $latestRecords->count()
        ];
    }

    /**
     * Lấy thống kê lịch sử từ bảng daily_self_uid_stats
     */
    private function getHistoricalDailyStats(Request $request)
    {
        $today = Carbon::now()->format('Y-m-d');
        
        $query = DailySelfUidStat::select(
                'stat_date as date',
                DB::raw('COUNT(DISTINCT self_uid) as unique_self_uids'),
                DB::raw('SUM(diamond_balance) as total_diamond'),
                DB::raw('SUM(bean_balance) as total_bean'),
                DB::raw('COUNT(*) as total_records')
            )
            ->where('stat_date', '<', $today)
            ->groupBy('stat_date')
            ->orderBy('stat_date', 'desc');

        // Áp dụng filter
        if ($request->filled('user_token')) {
            $query->where('user_token', $request->user_token);
        }
        if ($request->filled('self_uid')) {
            $query->where('self_uid', 'LIKE', '%' . $request->self_uid . '%');
        }

        // Giới hạn số ngày hiển thị (ví dụ: 30 ngày gần nhất)
        $query->limit(7);

        return $query->get()->map(function($item) {
            return (object) [
                'date' => $item->date,
                'unique_self_uids' => $item->unique_self_uids,
                'total_diamond' => $item->total_diamond,
                'total_bean' => $item->total_bean,
                'total_records' => $item->total_records
            ];
        })->toArray();
    }

    /**
     * Hiển thị thống kê tăng/giảm theo ngày (gộp chung)
     */
    public function dailyStats(Request $request)
    {
        $date = $request->get('date');

        if (!$date) {
            return redirect()->route('selfuid-tracking.index')
                ->with('error', 'Tham số ngày không hợp lệ');
        }

        // Tính toán cả thống kê tăng và giảm
        $increaseStats = $this->getIncreaseStatsByDate($date, $request);
        $decreaseStats = $this->getDecreaseStatsByDate($date, $request);

        $title = 'Thống kê Diamond và Bean dao động';

        return view('admin.selfuid-tracking.daily-stats', compact(
            'increaseStats', 'decreaseStats', 'date', 'title'
        ));
    }

    /**
     * Lấy thống kê tổng lượng giảm diamond và bean theo ngày
     */
    private function getDecreaseStatsByDate($date, Request $request)
    {
        // Tính toán thống kê giảm cho ngày cụ thể
        $query = SelfUidTracking::whereDate('created_at', $date);

        // Áp dụng filter nếu có
        if ($request->filled('user_token')) {
            $query->where('user_token', $request->user_token);
        }
        if ($request->filled('self_uid')) {
            $query->where('self_uid', 'LIKE', '%' . $request->self_uid . '%');
        }

        // Lấy tất cả records trong ngày
        $records = $query->orderBy('self_uid')->orderBy('created_at')->get();


        $totalDiamondDecrease = 0;
        $totalBeanDecrease = 0;
        $totalDecreaseCount = 0;
        $userDetails = [];
        $selfUidDetails = [];

        // Nhóm theo self_uid để tính toán sự thay đổi
        $groupedBySelfUid = $records->groupBy('self_uid');

        foreach ($groupedBySelfUid as $selfUid => $selfUidRecords) {
            $sortedRecords = $selfUidRecords->sortBy('created_at');
            $previousRecord = null;

            $selfUidDiamondDecrease = 0;
            $selfUidBeanDecrease = 0;
            $selfUidDecreaseCount = 0;

            foreach ($sortedRecords as $record) {
                if ($previousRecord) {
                    $diamondChange = $record->diamond_balance - $previousRecord->diamond_balance;
                    $beanChange = $record->bean_balance - $previousRecord->bean_balance;

                    if ($diamondChange < 0) {
                        $selfUidDiamondDecrease += abs($diamondChange);
                        $selfUidDecreaseCount++;
                    }
                    if ($beanChange < 0) {
                        $selfUidBeanDecrease += abs($beanChange);
                        if ($diamondChange >= 0) { // Chỉ đếm 1 lần nếu cả 2 đều giảm
                            $selfUidDecreaseCount++;
                        }
                    }
                }
                $previousRecord = $record;
            }

            if ($selfUidDiamondDecrease > 0 || $selfUidBeanDecrease > 0) {
                $totalDiamondDecrease += $selfUidDiamondDecrease;
                $totalBeanDecrease += $selfUidBeanDecrease;
                $totalDecreaseCount += $selfUidDecreaseCount;

                // Lấy thông tin user từ record đầu tiên
                $firstRecord = $sortedRecords->first();
                $user = User::where('user_token', $firstRecord->user_token)->first();

                $selfUidDetails[] = [
                    'self_uid' => $selfUid,
                    'user_token' => $firstRecord->user_token,
                    'user_name' => $user ? $user->name : 'Unknown',
                    'diamond_decrease' => $selfUidDiamondDecrease,
                    'bean_decrease' => $selfUidBeanDecrease,
                    'decrease_count' => $selfUidDecreaseCount
                ];

                // Cập nhật thống kê user
                $userToken = $firstRecord->user_token;
                if (!isset($userDetails[$userToken])) {
                    $userDetails[$userToken] = [
                        'user_token' => $userToken,
                        'user_name' => $user ? $user->name : 'Unknown',
                        'diamond_decrease' => 0,
                        'bean_decrease' => 0,
                        'decrease_count' => 0,
                        'uid_count' => 0
                    ];
                }
                $userDetails[$userToken]['diamond_decrease'] += $selfUidDiamondDecrease;
                $userDetails[$userToken]['bean_decrease'] += $selfUidBeanDecrease;
                $userDetails[$userToken]['decrease_count'] += $selfUidDecreaseCount;
                $userDetails[$userToken]['uid_count']++;
            }
        }

        // Sắp xếp selfUidDetails theo diamond_decrease giảm dần và lấy top 20
        usort($selfUidDetails, function($a, $b) {
            return $b['diamond_decrease'] - $a['diamond_decrease'];
        });
        $selfUidDetails = array_slice($selfUidDetails, 0, 20);

        // Chuyển userDetails từ associative array thành indexed array và sắp xếp
        $userDetailsArray = array_values($userDetails);
        usort($userDetailsArray, function($a, $b) {
            return $b['diamond_decrease'] - $a['diamond_decrease'];
        });

        return [
            'total_diamond_decrease' => $totalDiamondDecrease,
            'total_bean_decrease' => $totalBeanDecrease,
            'total_decrease_count' => $totalDecreaseCount,
            'affected_uid_count' => count($selfUidDetails),
            'user_details' => array_slice($userDetailsArray, 0, 10), // Top 10 users
            'self_uid_details' => $selfUidDetails // Top 20 Self UIDs
        ];
    }

    /**
     * Lấy thống kê tổng lượng tăng diamond và bean theo ngày
     */
    private function getIncreaseStatsByDate($date, Request $request)
    {
        // Tính toán thống kê tăng cho ngày cụ thể
        $query = SelfUidTracking::whereDate('created_at', $date);

        // Áp dụng filter nếu có
        if ($request->filled('user_token')) {
            $query->where('user_token', $request->user_token);
        }
        if ($request->filled('self_uid')) {
            $query->where('self_uid', 'LIKE', '%' . $request->self_uid . '%');
        }

        // Lấy tất cả records trong ngày
        $records = $query->orderBy('self_uid')->orderBy('created_at')->get();

        $totalDiamondIncrease = 0;
        $totalBeanIncrease = 0;
        $totalIncreaseCount = 0;
        $userDetails = [];
        $selfUidDetails = [];

        // Nhóm theo self_uid để tính toán sự thay đổi
        $groupedBySelfUid = $records->groupBy('self_uid');

        foreach ($groupedBySelfUid as $selfUid => $selfUidRecords) {
            $sortedRecords = $selfUidRecords->sortBy('created_at');
            $previousRecord = null;

            $selfUidDiamondIncrease = 0;
            $selfUidBeanIncrease = 0;
            $selfUidIncreaseCount = 0;

            foreach ($sortedRecords as $record) {
                if ($previousRecord) {
                    $diamondChange = $record->diamond_balance - $previousRecord->diamond_balance;
                    $beanChange = $record->bean_balance - $previousRecord->bean_balance;

                    if ($diamondChange > 0) {
                        $selfUidDiamondIncrease += $diamondChange;
                        $selfUidIncreaseCount++;
                    }
                    if ($beanChange > 0) {
                        $selfUidBeanIncrease += $beanChange;
                        if ($diamondChange <= 0) { // Chỉ đếm 1 lần nếu cả 2 đều tăng
                            $selfUidIncreaseCount++;
                        }
                    }
                }
                $previousRecord = $record;
            }

            if ($selfUidDiamondIncrease > 0 || $selfUidBeanIncrease > 0) {
                $totalDiamondIncrease += $selfUidDiamondIncrease;
                $totalBeanIncrease += $selfUidBeanIncrease;
                $totalIncreaseCount += $selfUidIncreaseCount;

                // Lấy thông tin user từ record đầu tiên
                $firstRecord = $sortedRecords->first();
                $user = User::where('user_token', $firstRecord->user_token)->first();

                $selfUidDetails[] = [
                    'self_uid' => $selfUid,
                    'user_token' => $firstRecord->user_token,
                    'user_name' => $user ? $user->name : 'Unknown',
                    'diamond_increase' => $selfUidDiamondIncrease,
                    'bean_increase' => $selfUidBeanIncrease,
                    'increase_count' => $selfUidIncreaseCount
                ];

                // Cập nhật thống kê user
                $userToken = $firstRecord->user_token;
                if (!isset($userDetails[$userToken])) {
                    $userDetails[$userToken] = [
                        'user_token' => $userToken,
                        'user_name' => $user ? $user->name : 'Unknown',
                        'diamond_increase' => 0,
                        'bean_increase' => 0,
                        'increase_count' => 0,
                        'uid_count' => 0
                    ];
                }
                $userDetails[$userToken]['diamond_increase'] += $selfUidDiamondIncrease;
                $userDetails[$userToken]['bean_increase'] += $selfUidBeanIncrease;
                $userDetails[$userToken]['increase_count'] += $selfUidIncreaseCount;
                $userDetails[$userToken]['uid_count']++;
            }
        }

        // Sắp xếp selfUidDetails theo diamond_increase giảm dần và lấy top 20
        usort($selfUidDetails, function($a, $b) {
            return $b['diamond_increase'] - $a['diamond_increase'];
        });
        $topSelfUids = array_slice($selfUidDetails, 0, 100);

        // Tạo Paginator thủ công
        $perPage = 20; // Số mục trên mỗi trang
        $currentPage = Paginator::resolveCurrentPage('page'); // Lấy trang hiện tại từ request
        $currentPageItems = array_slice($topSelfUids, ($currentPage - 1) * $perPage, $perPage);
        
        $paginatedSelfUids = new LengthAwarePaginator(
            $currentPageItems,
            count($topSelfUids), // Tổng số mục (tối đa 200)
            $perPage,
            $currentPage,
            [
                'path' => Paginator::resolveCurrentPath(),
                'pageName' => 'page',
            ]
        );


        // Chuyển userDetails từ associative array thành indexed array và sắp xếp
        $userDetailsArray = array_values($userDetails);
        usort($userDetailsArray, function($a, $b) {
            return $b['diamond_increase'] - $a['diamond_increase'];
        });

        return [
            'total_diamond_increase' => $totalDiamondIncrease,
            'total_bean_increase' => $totalBeanIncrease,
            'total_increase_count' => $totalIncreaseCount,
            'affected_uid_count' => count($selfUidDetails),
            'user_details' => array_slice($userDetailsArray, 0, 10), // Top 10 users
            'self_uid_details' => $paginatedSelfUids 
        ];
    }

    /**
     * Hiển thị danh sách tất cả selfUid tracking
     */
    public function index(Request $request)
    {
        // Set default date range if not provided
        if (!$request->has('date_from')) {
            $request->merge(['date_from' => now()->subDays(6)->format('Y-m-d')]);
        }
        if (!$request->has('date_to')) {
            $request->merge(['date_to' => now()->format('Y-m-d')]);
        }

        // Lấy danh sách self_uids với filtering
        $query = SelfUid::with('user');
        $selfUids = $this->applySelfUidFilters($query, $request);

        // Xử lý sắp xếp đặc biệt cho last_diamond_change
        $sortBy = $request->get('sort_by', 'last_diamond_change');
        $sortDirection = $request->get('sort_direction', 'desc');

        if ($sortBy === 'last_diamond_change') {
            // Lấy tất cả dữ liệu trước, sau đó sắp xếp
            $allSelfUids = $selfUids->get();
            $this->addLastDiamondChangeTimeToCollection($allSelfUids);

            // Sắp xếp theo last_diamond_change
            if ($sortDirection === 'desc') {
                $sorted = $allSelfUids->sortByDesc(function($item) {
                    return $item->last_diamond_change ? $item->last_diamond_change : '1900-01-01 00:00:00';
                });
            } else {
                $sorted = $allSelfUids->sortBy(function($item) {
                    return $item->last_diamond_change ? $item->last_diamond_change : '1900-01-01 00:00:00';
                });
            }

            // Tạo pagination thủ công
            $selfUids = $this->paginateCollection($sorted, 50, $request->get('page', 1));
        } else {
            // Sắp xếp bình thường
            $selfUids = $this->applySelfUidSorting($selfUids, $request);
            $selfUids = $selfUids->paginate(50);
            $this->addLastDiamondChangeTime($selfUids);
        }

        // Lấy thống kê tổng quan từ bảng SelfUid
        $overallStats = $this->getSelfUidOverallStats($request);

        // Lấy thống kê theo ngày (vẫn từ bảng tracking cũ)
        $dailyStats = $this->getDailyStats($request);

        $users = User::all();

        return view('admin.selfuid-tracking.index', compact(
            'selfUids',
            'users',
            'overallStats',
            'dailyStats'
        ));
    }

    /**
     * Hiển thị chi tiết lịch sử của một selfUid
     */
    public function show($selfUid, Request $request)
    {
        $query = SelfUidTracking::where('self_uid', $selfUid);

        // Filter theo user_token nếu có
        if ($request->filled('user_token')) {
            $query->where('user_token', $request->user_token);
        }

        // Filter theo ngày
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $history = $query->orderBy('created_at', 'desc')->paginate(100);

        // Lấy thống kê
        $stats = $this->getSelfUidStats($selfUid);

        return view('admin.selfuid-tracking.show', compact('history', 'selfUid', 'stats'));
    }

    /**
     * Hiển thị thống kê theo user_token
     */
    public function userStats($userToken)
    {
        $user = User::where('user_token', $userToken)->firstOrFail();
        
        // Thống kê tổng quan
        $stats = SelfUidTracking::getStatsByUserToken($userToken);
        
        // Top selfUid theo số lần request
        $topSelfUids = SelfUidTracking::where('user_token', $userToken)
            ->selectRaw('self_uid, COUNT(*) as request_count, MAX(created_at) as last_seen')
            ->groupBy('self_uid')
            ->orderBy('request_count', 'desc')
            ->limit(20)
            ->get();

        // Thống kê theo ngày (7 ngày gần nhất)
        $dailyStats = SelfUidTracking::where('user_token', $userToken)
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as requests, COUNT(DISTINCT self_uid) as unique_users')
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->get();

        return view('admin.selfuid-tracking.user-stats', compact('user', 'stats', 'topSelfUids', 'dailyStats'));
    }

    /**
     * API endpoint để lấy dữ liệu cho chart
     */
    public function chartData($selfUid, Request $request)
    {
        $days = $request->get('days', 7);
        $startDate = Carbon::now()->subDays($days);

        $data = SelfUidTracking::where('self_uid', $selfUid)
            ->where('created_at', '>=', $startDate)
            ->orderBy('created_at', 'asc')
            ->get();

        $chartData = [
            'labels' => [],
            'diamond' => [],
            'bean' => [],
            'room_count' => []
        ];

        foreach ($data as $item) {
            $chartData['labels'][] = $item->created_at->format('Y-m-d H:i');
            $chartData['diamond'][] = $item->diamond_balance;
            $chartData['bean'][] = $item->bean_balance;
            $chartData['room_count'][] = $item->room_count;
        }

        return response()->json($chartData);
    }

    /**
     * Lấy thống kê của một selfUid
     */
    private function getSelfUidStats($selfUid)
    {
        $latest = SelfUidTracking::where('self_uid', $selfUid)
            ->orderBy('created_at', 'desc')
            ->first();

        $earliest = SelfUidTracking::where('self_uid', $selfUid)
            ->orderBy('created_at', 'asc')
            ->first();

        $totalRequests = SelfUidTracking::where('self_uid', $selfUid)->count();

        $stats = [
            'total_requests' => $totalRequests,
            'first_seen' => $earliest ? $earliest->created_at : null,
            'last_seen' => $latest ? $latest->created_at : null,
            'current_diamond' => $latest ? $latest->diamond_balance : 0,
            'current_bean' => $latest ? $latest->bean_balance : 0,
            'current_room_count' => $latest ? $latest->room_count : 0,
            'current_device_name' => $latest ? $latest->device_name : null,
        ];

        if ($earliest && $latest && $earliest->id !== $latest->id) {
            $stats['diamond_change'] = $latest->diamond_balance - $earliest->diamond_balance;
            $stats['bean_change'] = $latest->bean_balance - $earliest->bean_balance;
            $stats['room_count_change'] = $latest->room_count - $earliest->room_count;
        }

        return $stats;
    }

    /**
     * Xóa dữ liệu tracking cũ (để dọn dẹp database)
     */
    public function cleanup(Request $request)
    {
        $days = $request->get('days', 30);
        $cutoffDate = Carbon::now()->subDays($days);

        $deletedCount = SelfUidTracking::where('created_at', '<', $cutoffDate)->delete();

        return response()->json([
            'success' => true,
            'deleted_count' => $deletedCount,
            'message' => "Đã xóa {$deletedCount} bản ghi cũ hơn {$days} ngày"
        ]);
    }

    /**
     * Hiển thị thống kê tối ưu database
     */
    public function optimizationStats()
    {
        // Thống kê tổng quan
        $totalRecords = SelfUidTracking::count();
        $uniqueSelfUids = SelfUidTracking::distinct('self_uid')->count();
        $uniqueUserTokens = SelfUidTracking::distinct('user_token')->count();

        // Thống kê client cũ vs mới
        $oldClientRecords = SelfUidTracking::where('is_old_client', true)->count();
        $newClientRecords = SelfUidTracking::where('is_old_client', false)->count();

        // Thống kê theo ngày (7 ngày gần nhất)
        $dailyStats = SelfUidTracking::where('created_at', '>=', Carbon::now()->subDays(7))
            ->selectRaw('
                DATE(created_at) as date,
                COUNT(*) as records_saved,
                SUM(CASE WHEN is_old_client = 1 THEN 1 ELSE 0 END) as old_client_records,
                SUM(CASE WHEN is_old_client = 0 THEN 1 ELSE 0 END) as new_client_records
            ')
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->get();

        return response()->json([
            'total_records' => $totalRecords,
            'unique_self_uids' => $uniqueSelfUids,
            'unique_user_tokens' => $uniqueUserTokens,
            'old_client_records' => $oldClientRecords,
            'new_client_records' => $newClientRecords,
            'old_client_percentage' => $totalRecords > 0 ? round(($oldClientRecords / $totalRecords) * 100, 2) : 0,
            'daily_stats' => $dailyStats,
            'optimization_note' => 'Chỉ lưu khi có thay đổi Diamond/Bean/Room Count, hỗ trợ client cũ'
        ]);
    }

    /**
     * Hiển thị danh sách device_name không hoạt động trong 30 phút gần nhất
     */
    public function inactiveDevices(Request $request)
    {
        $minutes = $request->get('minutes', 30);
        $sortBy = $request->get('sort_by', 'device_name');
        $sortDirection = $request->get('sort_direction', 'desc');

        // Validate minutes parameter
        if (!is_numeric($minutes) || $minutes < 1 || $minutes > 1440) { // Max 24 hours
            $minutes = 30;
        }

        // Validate sort parameters
        $allowedSortFields = ['device_name', 'total_devices', 'last_activity'];
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'device_name';
        }

        if (!in_array($sortDirection, ['asc', 'desc'])) {
            $sortDirection = 'desc';
        }

        $inactiveDevices = SelfUid::getInactiveDeviceNames($minutes);

        // Apply sorting
        if ($sortDirection === 'asc') {
            $inactiveDevices = $inactiveDevices->sortBy($sortBy);
        } else {
            $inactiveDevices = $inactiveDevices->sortByDesc($sortBy);
        }

        // Thống kê tổng quan
        $totalInactiveDevices = $inactiveDevices->count();
        // $totalInactiveSelfUids = $inactiveDevices->sum('total_devices');
        // $totalInactiveDiamond = $inactiveDevices->sum('total_diamond');
        // $totalInactiveBean = $inactiveDevices->sum('total_bean');

        // Thống kê so sánh với active devices
        $activeDevicesCount = SelfUid::where('requested_at', '>=', Carbon::now()->subMinutes($minutes))
            ->whereNotNull('device_name')
            ->where('device_name', '!=', '')
            ->distinct('device_name')
            ->count();

        $stats = [
            'minutes' => $minutes,
            'total_inactive_devices' => $totalInactiveDevices,
            // 'total_inactive_self_uids' => $totalInactiveSelfUids,
            // 'total_inactive_diamond' => $totalInactiveDiamond,
            // 'total_inactive_bean' => $totalInactiveBean,
            'active_devices_count' => $activeDevicesCount,
            'total_devices_count' => $totalInactiveDevices + $activeDevicesCount
        ];

        return view('admin.selfuid-tracking.inactive-devices', compact('inactiveDevices', 'stats'));
    }
}
