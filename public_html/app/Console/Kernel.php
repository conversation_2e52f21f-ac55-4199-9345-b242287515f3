<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')->hourly();

        // Tổng hợp dữ liệu self_uid hàng ngày vào 23:59
        // $schedule->command('selfuid:generate-daily-stats --force')
        //     ->dailyAt('23:59')
        //     ->withoutOverlapping()
        //     ->runInBackground()
        //     ->appendOutputTo(storage_path('logs/daily-stats.log'));

        // Tạo thống kê tổng hợp hàng ngày vào 00:00 (cho ngày hôm trước)
        $schedule->command('daily-stats:generate --force')
            ->dailyAt('00:00')
            ->withoutOverlapping()
            ->runInBackground()
            ->appendOutputTo(storage_path('logs/daily-summary-stats.log'));
    }
    // * * * * * cd /home/<USER>/public_html && php artisan schedule:run >> /dev/null 2>&1

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
