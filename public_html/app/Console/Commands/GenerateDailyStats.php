<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DailyStatsService;
use Carbon\Carbon;

class GenerateDailyStats extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'daily-stats:generate {date?} {--force} {--range=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tạo thống kê tổng hợp hàng ngày cho trang admin selfuid-tracking';

    /**
     * @var DailyStatsService
     */
    protected $statsService;

    /**
     * Create a new command instance.
     */
    public function __construct(DailyStatsService $statsService)
    {
        parent::__construct();
        $this->statsService = $statsService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $dateInput = $this->argument('date');
        $force = $this->option('force');
        $range = $this->option('range');

        // X<PERSON> lý range nếu có
        if ($range) {
            return $this->handleDateRange($range, $force);
        }

        // Xác định ngày cần tổng hợp
        if ($dateInput) {
            try {
                $targetDate = Carbon::parse($dateInput)->format('Y-m-d');
            } catch (\Exception $e) {
                $this->error("Định dạng ngày không hợp lệ: {$dateInput}. Vui lòng sử dụng định dạng Y-m-d (ví dụ: 2025-11-05)");
                return 1;
            }
        } else {
            // Mặc định là ngày hôm qua (vì chạy vào 00:00 cho ngày hôm trước)
            $targetDate = Carbon::yesterday()->format('Y-m-d');
        }

        return $this->handleSingleDate($targetDate, $force);
    }

    /**
     * Xử lý tạo thống kê cho một ngày
     */
    private function handleSingleDate($targetDate, $force)
    {
        $this->info("Bắt đầu tạo thống kê cho ngày: {$targetDate}");

        $result = $this->statsService->generateStatsForDate($targetDate, $force);

        if ($result['success']) {
            if ($result['stats']) {
                $stats = $result['stats'];
                $this->info("✓ {$result['message']}");
                $this->line("  - Số UID thay đổi: {$stats->uid_changes_count}");
                $this->line("  - Số lần tăng: {$stats->increase_count}");
                $this->line("  - Số lần giảm: {$stats->decrease_count}");
                $this->line("  - Tổng Diamond: " . number_format($stats->total_diamond));
                $this->line("  - Tổng Bean: " . number_format($stats->total_bean));
                $this->line("  - Số User duy nhất: {$stats->unique_users_count}");
            } else {
                $this->info("✓ {$result['message']}");
            }
            return 0;
        } else {
            $this->error("✗ {$result['message']}");
            return 1;
        }
    }

    /**
     * Xử lý tạo thống kê cho khoảng thời gian
     */
    private function handleDateRange($range, $force)
    {
        try {
            $dates = explode(',', $range);
            if (count($dates) !== 2) {
                $this->error("Định dạng range không hợp lệ. Sử dụng: --range=2025-11-01,2025-11-05");
                return 1;
            }

            $startDate = Carbon::parse(trim($dates[0]));
            $endDate = Carbon::parse(trim($dates[1]));

            if ($startDate->gt($endDate)) {
                $this->error("Ngày bắt đầu phải nhỏ hơn ngày kết thúc");
                return 1;
            }

        } catch (\Exception $e) {
            $this->error("Định dạng ngày không hợp lệ trong range: {$range}");
            return 1;
        }

        $this->info("Tạo thống kê cho khoảng thời gian: {$startDate->format('Y-m-d')} đến {$endDate->format('Y-m-d')}");

        $totalProcessed = 0;
        $totalErrors = 0;

        $current = $startDate->copy();
        while ($current->lte($endDate)) {
            $dateStr = $current->format('Y-m-d');
            
            try {
                $result = $this->statsService->generateStatsForDate($dateStr, $force);
                
                if ($result['success']) {
                    if ($result['stats']) {
                        $stats = $result['stats'];
                        $this->line("✓ {$dateStr}: {$stats->uid_changes_count} UID thay đổi");
                        $totalProcessed++;
                    } else {
                        $this->line("- {$dateStr}: Không có dữ liệu");
                    }
                } else {
                    $totalErrors++;
                    $this->line("✗ {$dateStr}: {$result['message']}");
                }
            } catch (\Exception $e) {
                $totalErrors++;
                $this->error("✗ {$dateStr}: Lỗi - {$e->getMessage()}");
            }

            $current->addDay();
        }

        $this->info("\nTóm tắt:");
        $this->line("- Đã xử lý: {$totalProcessed} ngày");
        $this->line("- Lỗi: {$totalErrors} ngày");

        return $totalErrors > 0 ? 1 : 0;
    }
}
